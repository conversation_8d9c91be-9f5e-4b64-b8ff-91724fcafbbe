package org.jeecg.modules.info.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.info.entity.PdNewsFlash;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.wechat.vo.news.AppnewsVO;

/**
 * @Description: 新闻快讯
 * @Author: jeecg-boot
 * @Date:   2025-05-05
 * @Version: V1.0
 */
public interface PdNewsFlashMapper extends BaseMapper<PdNewsFlash> {

    /**
     * 分页查询新闻快讯，按时间降序排列
     * @param page 分页对象
     * @return 分页结果
     */
    IPage<AppnewsVO> getNewsFlashByPage(Page<AppnewsVO> page);

}
