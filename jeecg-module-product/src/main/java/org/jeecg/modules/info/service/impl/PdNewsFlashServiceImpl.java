package org.jeecg.modules.info.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.info.entity.PdNewsFlash;
import org.jeecg.modules.info.mapper.PdNewsFlashMapper;
import org.jeecg.modules.info.service.IPdNewsFlashService;
import org.jeecg.modules.wechat.vo.news.AppnewsListVO;
import org.jeecg.modules.wechat.vo.news.AppnewsVO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 新闻快讯
 * @Author: jeecg-boot
 * @Date:   2025-05-05
 * @Version: V1.0
 */
@Service
public class PdNewsFlashServiceImpl extends ServiceImpl<PdNewsFlashMapper, PdNewsFlash> implements IPdNewsFlashService {



    /**
     * 分页获取新闻快讯数据
     *
     * @param pageNo 页码，从1开始
     * @param pageSize 每页大小
     * @return 包含日期标题和快讯列表的VO对象
     */
    @Override
    public AppnewsListVO getNewsFlashByPage(Integer pageNo, Integer pageSize) {
        // 参数校验
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 创建返回对象
        AppnewsListVO resultVO = new AppnewsListVO();

        // 使用LocalDate来处理日期比较，更加精确和简洁
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        // 创建日期格式化对象
        SimpleDateFormat monthDayFormat = new SimpleDateFormat("MM/dd");

        // 使用MyBatis XML分页查询，确保时间排序正确
        Page<AppnewsVO> page = new Page<>(pageNo, pageSize);
        IPage<AppnewsVO> xmlResult = baseMapper.getNewsFlashByPage(page);

        List<AppnewsVO> newsList = xmlResult.getRecords();

        // 如果没有数据，返回空结果
        if (newsList == null || newsList.isEmpty()) {
            resultVO.setTopDate("暂无数据");
            resultVO.setCompanyList(new ArrayList<>());
            return resultVO;
        }

        // 需要获取第一条记录的实际时间来设置topDate，因为XML查询只返回了格式化后的时间
        // 所以我们需要查询第一条记录的完整信息
        String firstNewsId = newsList.get(0).getId();
        PdNewsFlash firstNews = baseMapper.selectById(firstNewsId);

        if (firstNews != null && firstNews.getOrderTime() != null) {
            Date firstNewsDate = firstNews.getOrderTime();
            LocalDate firstNewsLocalDate = firstNewsDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

            // 设置顶部日期显示逻辑
            String topDate;
            if (firstNewsLocalDate.equals(today)) {
                topDate = "今天";
            } else if (firstNewsLocalDate.equals(yesterday)) {
                topDate = "昨天";
            } else {
                // 显示月份和日期 (MM/dd格式)
                topDate = monthDayFormat.format(firstNewsDate);
            }
            resultVO.setTopDate(topDate);
        } else {
            resultVO.setTopDate("今天");
        }

        // 直接使用XML查询返回的AppnewsVO列表，时间已经在SQL中格式化好了
        resultVO.setCompanyList(newsList);
        return resultVO;
    }


}
