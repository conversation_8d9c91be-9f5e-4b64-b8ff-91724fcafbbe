package org.jeecg.modules.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SysTenantDto implements Serializable {

    private Integer id;

    private String name;

    @ApiModelProperty(value = "多租户id")
    private String tenantId;

    @ApiModelProperty(value = "多租户id集合")
    private List<String> tenantIds;

    @ApiModelProperty(value = "多租户id集合-前端传参")
    private List<String> tenantList;

    private String orderBy;

    @ApiModelProperty(value = "业务城市编码列表")
    private List<String> businessCities;

    @ApiModelProperty(value = "业务类型列表 (0-车险,1-财险,2-增值服务)")
    private List<Integer> businessTypes;

    @ApiModelProperty(value = "是否查询业务城市为空的记录")
    private Boolean businessCitiesEmpty;
}
